<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="mini"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
      class="project-search-form"
    >
      <div class="search-form-container">
        <el-form-item label="作品名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入作品名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="作品类别" prop="category">
          <el-select
            v-model="queryParams.category"
            placeholder="作品类别"
            clearable
          >
            <el-option
              v-for="dict in filteredWorkTypes"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域" prop="district">
          <el-cascader
            v-model="queryParams.district"
            :options="addressOptions.filter(opt => opt.id === '110000')"
            :props="{ label: 'name', value: 'id' }"
            clearable
            placeholder="请选择所属区域"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="活动对象" prop="participantType">
          <el-select
            v-model="queryParams.participantType"
            placeholder="活动对象"
            clearable
          >
            <el-option
              v-for="dict in dict.type.grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="作品状态" prop="articleStatus">
          <el-select
            v-model="queryParams.articleStatus"
            placeholder="作品状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.article_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索
          </el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置
          </el-button>
        </el-form-item>
      </div>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          :disabled="isAddButtonDisabled"
          v-hasPermi="['system:project:add']"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-thumb"
          size="mini"
          :disabled="multiple"
          @click="handleRecommend"
          v-hasPermi="['system:project:recommend']"
        >推荐
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="multiple"
          @click="handleExport"
          v-hasPermi="['system:project:export']"
        >导出
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportAll"
          :disabled="exportButtonDisabled"
          v-hasPermi="['system:project:exportAll']"
        >
          {{ exportButtonDisabled ? `导出全部(${exportButtonCountdown}s)` : '导出全部' }}
        </el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['system:project:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['system:project:remove']">删除</el-button>
            </el-col> -->

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="projectList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      style="width: 100%"
      class="table-with-scroll"
      :height="'100%'"
    >
      <!--      <el-table-column type="selection" width="55" align="center"/>-->
      <el-table-column type="selection" width="55" align="center" fixed="left"/>
      <el-table-column label="作品名称" align="center" prop="name" width="150" fixed="left"/>
      <el-table-column label="作品类别" align="center" prop="category" width="130">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.work_type"
            :value="scope.row.category"
          />
        </template>
      </el-table-column>
      <el-table-column label="所属区域" align="center" prop="addressContent" width="120"/>
      <el-table-column label="辅导老师" align="center" prop="tutorName" width="100"/>
      <el-table-column
        label="辅导老师联系方式"
        align="center"
        prop="tutorContact"
        width="130"
      />

      <el-table-column
        label="作品简介"
        align="center"
        prop="descriptionText"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column label="活动对象" align="center" prop="participantType" width="120">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.grade"
            :value="scope.row.participantType"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="参与人数"
        align="center"
        prop="participantCount"
        width="80"
      />
      <el-table-column label="得分" align="center" prop="score" width="80"/>
      <el-table-column label="作品状态" align="center" prop="articleStatus" width="100">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.article_status"
            :value="scope.row.articleStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="区级审核" align="center" prop="districtAuditStatus" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="showDistrictAuditDetail(scope.row)"
            v-if="scope.row.districtAuditStatus !== undefined"
          >
            <dict-tag
              :options="dict.type.district_audit_status"
              :value="scope.row.districtAuditStatus"
            />
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="市级审核" align="center" prop="auditStatus" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="showCityAuditDetail(scope.row)"
            v-if="scope.row.auditStatus !== undefined"
          >
            <dict-tag
              :options="dict.type.city_audit_status"
              :value="scope.row.auditStatus"
            />
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="平均分"
        align="center"
        prop="averageScore"
        width="100"
        sortable="custom"
      >
        <template slot-scope="scope">
          {{ scope.row.averageScore || '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="审核员" align="center" prop="auditor" width="80"/>
      <el-table-column label="审核备注" align="center" prop="auditRemark" width="120" show-overflow-tooltip/> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width responsive-actions"
        fixed="right"
        width="160"
      >
        <template slot-scope="scope">
          <div class="action-buttons-container" :ref="`actionContainer-${scope.$index}`">
            <!-- 始终显示的详情按钮 -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleDetail(scope.row)"
              v-hasPermi="['system:project:detail']"
              class="action-btn primary-action"
            >详情
            </el-button>

            <!-- 可能被折叠的按钮 -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              :disabled="scope.row.auditStatus === 1 || (scope.row.districtAuditStatus === 1 && scope.row.auditStatus !== 2)
                        || scope.row.articleStatus === 1"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:project:edit']"
              class="action-btn collapsible-action"
              :class="{ 'hidden-action': shouldHideAction(scope.$index, 1) }"
            >修改
            </el-button>

            <el-tooltip
              :content="getScoreButtonTooltip(scope.row)"
              placement="top"
              :disabled="!isScoreButtonDisabled(scope.row)"
            >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleScoreAction(scope.row)"
                :disabled="isScoreButtonDisabled(scope.row)"
                v-hasPermi="['system:project:score']"
                class="action-btn collapsible-action"
                :class="{ 'hidden-action': shouldHideAction(scope.$index, 2) }"
              >{{ getScoreButtonText() }}
              </el-button>
            </el-tooltip>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-upload"
              :disabled="scope.row.auditStatus === 1 || (scope.row.districtAuditStatus === 1 && scope.row.auditStatus !== 2)"
              @click="handleApply(scope.row)"
              v-hasPermi="['system:project:apply']"
              class="action-btn collapsible-action"
              :class="{ 'hidden-action': shouldHideAction(scope.$index, 3) }"
            >申报
            </el-button>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleAudit(scope.row)"
              v-hasPermi="['system:project:audit']"
              class="action-btn collapsible-action"
              :class="{ 'hidden-action': shouldHideAction(scope.$index, 4) }"
            >审核
            </el-button>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:project:remove']"
              class="action-btn collapsible-action"
              :class="{ 'hidden-action': shouldHideAction(scope.$index, 5) }"
            >删除
            </el-button>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              size="mini"
              @command="(command) => handleMoreCommand(command, scope.row)"
              v-if="hasHiddenActions(scope.$index)"
              class="more-actions-dropdown"
            >
              <el-button size="mini" type="text" icon="el-icon-more">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-if="shouldHideAction(scope.$index, 1)"
                  command="update"
                  icon="el-icon-edit"
                  :disabled="scope.row.auditStatus === 1 || (scope.row.districtAuditStatus === 1 && scope.row.auditStatus !== 2)
                            || scope.row.articleStatus === 1"
                  v-hasPermi="['system:project:edit']"
                >修改</el-dropdown-item>

                <el-dropdown-item
                  v-if="shouldHideAction(scope.$index, 2)"
                  command="score"
                  icon="el-icon-edit"
                  :disabled="isScoreButtonDisabled(scope.row)"
                  v-hasPermi="['system:project:score']"
                >{{ getScoreButtonText() }}</el-dropdown-item>

                <el-dropdown-item
                  v-if="shouldHideAction(scope.$index, 3)"
                  command="apply"
                  icon="el-icon-upload"
                  :disabled="scope.row.auditStatus === 1 || (scope.row.districtAuditStatus === 1 && scope.row.auditStatus !== 2)"
                  v-hasPermi="['system:project:apply']"
                >申报</el-dropdown-item>

                <el-dropdown-item
                  v-if="shouldHideAction(scope.$index, 4)"
                  command="audit"
                  icon="el-icon-view"
                  v-hasPermi="['system:project:audit']"
                >审核</el-dropdown-item>

                <el-dropdown-item
                  v-if="shouldHideAction(scope.$index, 5)"
                  command="delete"
                  icon="el-icon-delete"
                  v-hasPermi="['system:project:remove']"
                >删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 20, 30, 50, 100, 200]"
      @pagination="getList"
      class="pagination-container"
    />

    <!-- 添加或修改作品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="850px" append-to-body>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="160px"
        label-position="left"
        :disabled="handleType === 'detail'"
        class="project-dialog-form"
      >
        <el-row :gutter="34">
          <el-col :span="12">
            <el-form-item label="作品类别" prop="category">
              <el-select
                style="width: 100%"
                v-model="form.category"
                placeholder="作品类别"
                clearable
              >
                <el-option
                  v-for="dict in filteredWorkTypes"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="学校所属区域" prop="district">
              <el-cascader
                style="width: 100%"
                v-model="form.district"
                :options="addressOptions.filter(opt => opt.id === '110000')"
                :props="{ label: 'name', value: 'id' }"
                ref="cascaderRef"
                @change="handleOnAddressChange"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="辅导老师" prop="tutors">
              <div class="tutors-container">
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-plus"
                  @click="handleType === 'detail' ? viewTutorDetail() : showTutorDialog()"
                  :disabled="handleType !== 'detail' && form.tutors && form.tutors.length >= 2"
                >
                  {{ handleType === 'detail' ? '查看辅导老师' : (form.tutors && form.tutors.length > 0 ? '查看/编辑辅导老师' : '添加辅导老师') }}
                </el-button>
                <div v-if="form.tutors && form.tutors.length > 0" class="tutors-list">
                  <div v-for="(tutor, index) in form.tutors" :key="index" class="tutor-item">
                    <span
                      :class="{'tutor-clickable': handleType === 'detail'}"
                      @click="handleType === 'detail' ? viewTutorDetail(index) : null"
                    >{{ index + 1 }}. {{ tutor.name }}</span>
                    <el-button
                      v-if="handleType !== 'detail'"
                      type="text"
                      icon="el-icon-edit"
                      @click="editTutor(index)"
                    >编辑</el-button>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="活动对象" prop="participantType">
              <el-select
                style="width: 100%"
                v-model="form.participantType"
                placeholder="活动对象"
                clearable
                disabled
              >
                <el-option
                  v-for="dict in dict.type.grade"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="参与人数" prop="participantCount">
              <!--              <el-select v-model="form.participantCount" placeholder="请选择参与人数" style="width: 100%">-->
              <!--                <el-option-->
              <!--                  v-for="num in 5"-->
              <!--                  :key="num"-->
              <!--                  :label="`${num}`"-->
              <!--                  :value="num"-->
              <!--                />-->
              <!--              </el-select>-->
              <el-select v-model="form.participantCount" placeholder="请选择参与人数" style="width: 100%">
                <el-option
                  v-for="num in participantCountOptions"
                  :key="num"
                  :label="`${num}`"
                  :value="num"
                />
              </el-select>

            </el-form-item>
            <el-form-item label="作品简介" prop="descriptionText">
              <!-- 新增的文本输入框 -->
              <el-input
                v-model="form.descriptionText"
                placeholder="请输入作品简介（不超过300字）"
                type="textarea"
                :rows="4"
                :maxlength="300"
                show-word-limit
                :disabled="handleType === 'detail'"
                @input="handleDescriptionTextInput"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="简介附件" prop="description">
              <FileUpload
                :fileSize="20"
                :limit="1"
                :fileType="['doc','docx']"
                :value="form.description"
                :disabled="handleType === 'detail'"
                @input="handleChangeFile($event, 'description')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作品名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入作品名称"/>
            </el-form-item>
            <el-form-item label="创作档案/演示视频" prop="creationFile">
              <ChunkedUpload
                :value="form.creationFile"
                :fileSize="500"
                :fileType="['mp4', 'zip']"
                :disabled="handleType === 'detail'"
                @input="handleChangeFile($event, 'creationFile')"
              />
            </el-form-item>
            <el-form-item label="作品/实践报告/教学案例报告" prop="workUrl">
              <template v-if="form.category != '2'">
                <!-- :fileType="fileType" -->
                <div style="position: relative; width: 100%;">
                  <FileUpload
                    :fileSize="form.category == '1' ? 20 : 100"
                    :value="form.workUrl"
                    :limit="1"
                    :fileType="['doc','docx', 'pdf', 'jpg', 'png', 'bmp', 'zip', 'rar']"
                    @input="handleChangeFile($event, 'workUrl')"
                    :disabled="handleType === 'detail'"
                    style="width: 100%;"
                  />
                  <div style="margin-top: 5px; color: #909399; font-size: 12px;">多个文件请上传压缩包(zip或rar格式)</div>
                </div>
              </template>
              <template v-else>
                <el-input v-model="form.workUrl" placeholder="请输入作品网页链接" style="width: 100%;"/>
              </template>
            </el-form-item>
            <el-form-item label="创作者声明" prop="creatorStatement">
              <FileUpload
                :fileSize="20"
                :limit="1"
                :value="form.creatorStatement"
                :isShowTip="false"
                :disabled="handleType === 'detail'"
                @input="handleChangeFile($event, 'creatorStatement')"
                style="width: 100%; display: block; margin-bottom: 10px;"
              />
              <div style="margin-left: -12px;">
                <el-button type="text" :disabled="false" @click="importTemplate">
                  下载创作者声明
                </el-button>
                <div style="margin-top: 5px; color: #f56c6c; font-size: 12px; margin-left: 12px;">
                  请下载创作者声明后，签字扫描后上传
                </div>
              </div>
            </el-form-item>

            <el-form-item
              label="申报表文件"
              prop="declarationUrl"
              v-if="handleType === 'detail' && form.declarationUrl"
            >
              <a
                :href="form.declarationUrl"
                target="_blank"
                style="color: #606266; text-decoration: none;"
                class="declaration-link"
              >
                查看
              </a>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="handleType !== 'detail'">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="评分"
      :visible.sync="scoreOpen"
      width="450px"
      append-to-body
    >
      <el-form
        ref="form1"
        :model="form1"
        :rules="rules1"
        label-width="80px"
        label-position="top"
      >
        <el-form-item label="评分" prop="score">
          <el-input-number
            style="width: 100%"
            v-model="form1.score"
            controls-position="right"
            :min="0"
            :controls="false"
            placeholder="请输入评分"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="scoreOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveScore">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 在文件末尾的el-dialog之后添加 -->
    <el-dialog
      title="申报表上传"
      :visible.sync="applyOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="applyForm"
        :model="applyForm"
        :rules="applyRules"
        label-width="120px"
      >
        <div style="margin-bottom: 15px; padding-left: 120px; display: flex; flex-direction: column;">
          <div style="margin-left: -15px;">
            <el-button type="text" @click="downloadDeclarationTemplate">下载申报表</el-button>
            <div style="margin-top: 10px; color: #999; font-size: 12px; text-align: left; margin-left: 12px;">
              下载申报表后签字盖章，拍照或扫描后上传
            </div>
          </div>
        </div>
        <el-form-item label="申报表文件" prop="declarationUrl">
          <FileUpload
            :fileSize="20"
            :limit="1"
            :fileType="['jpg','jpeg','png','pdf']"
            :value="applyForm.declarationUrl"
            @input="handleChangeDeclarationFile"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="applyOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitApply">提 交</el-button>
      </div>
    </el-dialog>

    <!-- 审核 -->
    <el-dialog
      title="作品审核"
      :visible.sync="auditOpen"
      width="500px"
      append-to-body
      custom-class="custom-dialog"
    >
      <el-form
        ref="auditForm"
        :model="auditForm"
        :rules="auditRules"
        label-width="0px"
        class="custom-form"
      >
        <el-form-item label="审核结果" prop="auditStatus" class="custom-form-item">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1" class="custom-radio">审核通过</el-radio>
            <el-radio :label="2" class="custom-radio">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark" class="custom-form-item textarea-form-item">
          <el-input
            type="textarea"
            v-model="auditForm.auditRemark"
            placeholder="请输入审核备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">提 交</el-button>
      </div>
    </el-dialog>

    <!-- 推荐 -->
    <el-dialog
      title="作品推荐"
      :visible.sync="recommendOpen"
      width="500px"
      append-to-body
      custom-class="custom-dialog"
    >
      <el-form
        ref="recommendForm"
        :model="recommendForm"
        :rules="recommendRules"
        label-width="0px"
        class="custom-form"
      >
        <el-form-item label="推荐结果" prop="recommendStatus" class="custom-form-item">
          <el-radio-group v-model="recommendForm.recommendStatus">
            <el-radio :label="1" class="custom-radio">推荐</el-radio>
            <el-radio :label="2" class="custom-radio">不推荐</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="推荐备注" prop="recommendRemark" class="custom-form-item textarea-form-item">
          <el-input
            type="textarea"
            v-model="recommendForm.recommendRemark"
            placeholder="请输入推荐备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="recommendOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitRecommend">提 交</el-button>
      </div>
    </el-dialog>

    <!-- 区级审核详情 -->
    <el-dialog
      title="区级审核详情"
      :visible.sync="districtAuditDetailOpen"
      width="400px"
      append-to-body
    >
      <div class="audit-detail">
        <div class="audit-item">
          <span class="label">审核状态：</span>
          <span class="value">
            <dict-tag
              :options="dict.type.district_audit_status"
              :value="districtAuditDetail.districtAuditStatus"
            />
          </span>
        </div>
        <div class="audit-item">
          <span class="label">审核员：</span>
          <span class="value">{{ districtAuditDetail.districtAuditor || '-' }}</span>
        </div>
        <div class="audit-item">
          <span class="label">{{ districtAuditDetail.districtAuditStatus === 1 ? '推荐理由：' : '不推荐理由：' }}</span>
          <span class="value">{{ districtAuditDetail.districtAuditRemark || '-' }}</span>
        </div>
      </div>
    </el-dialog>

    <!-- 市级审核详情 -->
    <el-dialog
      title="市级审核详情"
      :visible.sync="cityAuditDetailOpen"
      width="400px"
      append-to-body
    >
      <div class="audit-detail">
        <div class="audit-item">
          <span class="label">审核状态：</span>
          <span class="value">
            <dict-tag
              :options="dict.type.city_audit_status"
              :value="cityAuditDetail.cityAuditStatus"
            />
          </span>
        </div>
        <div class="audit-item">
          <span class="label">审核员：</span>
          <span class="value">{{ cityAuditDetail.cityAuditor || '-' }}</span>
        </div>
        <div class="audit-item">
          <span class="label">审核备注：</span>
          <span class="value">{{ cityAuditDetail.cityAuditRemark || '-' }}</span>
        </div>
      </div>
    </el-dialog>

    <!-- 添加固定滚动条 -->
    <div class="fixed-scroll-container" ref="fixedScrollContainer" v-show="showFixedScroll">
      <div class="fixed-scroll-content" :style="{ width: tableWidth + 'px' }"></div>
    </div>

    <!-- 辅导老师信息对话框 -->
    <el-dialog
      :title="tutorDialogTitle"
      :visible.sync="tutorDialogVisible"
      width="600px"
      append-to-body
    >
      <el-form
        ref="tutorForm"
        :model="tutorForm"
        :rules="tutorRules"
        label-width="100px"
        class="tutor-form"
      >
        <el-tabs v-model="activeTutorTab" type="card">
          <el-tab-pane
            v-for="(tutor, index) in tutorForm.tutors"
            :key="index"
            :label="'辅导老师' + (index + 1)"
            :name="'tutor' + index"
          >
            <el-form-item :label="'姓名'" :prop="'tutors.' + index + '.name'" :rules="tutorRules.name">
              <el-input v-model="tutor.name" placeholder="请输入辅导老师姓名"></el-input>
            </el-form-item>
            <el-form-item :label="'性别'" :prop="'tutors.' + index + '.gender'" :rules="tutorRules.gender">
              <el-radio-group v-model="tutor.gender">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="'联系方式'" :prop="'tutors.' + index + '.contact'" :rules="tutorRules.contact">
              <el-input v-model="tutor.contact" placeholder="请输入联系方式"></el-input>
            </el-form-item>
            <el-form-item :label="'邮箱'" :prop="'tutors.' + index + '.email'" :rules="tutorRules.email">
              <el-input v-model="tutor.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
            <el-form-item :label="'职务/职称'" :prop="'tutors.' + index + '.title'" :rules="tutorRules.title">
              <el-input v-model="tutor.title" placeholder="请输入职务/职称"></el-input>
            </el-form-item>
            <el-form-item :label="'工作单位'" :prop="'tutors.' + index + '.workUnit'" :rules="tutorRules.workUnit">
              <el-input v-model="tutor.workUnit" placeholder="请输入工作单位"></el-input>
            </el-form-item>
            <el-form-item :label="'所属地区'" :prop="'tutors.' + index + '.district'" :rules="tutorRules.district">
              <el-cascader
                v-model="tutor.district"
                :options="addressOptions.filter(opt => opt.id === '110000')"
                :props="{ label: 'name', value: 'id', expandTrigger: 'hover' }"
                @change="(value) => handleTutorDistrictChange(value, index)"
                placeholder="请选择所属地区"
              ></el-cascader>
            </el-form-item>
            <div class="tutor-actions" v-if="index === 1">
              <el-button type="danger" size="mini" @click="removeTutor(index)">删除该辅导老师</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="tutor-dialog-footer">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="addTutor"
            v-if="tutorForm.tutors.length < 2"
          >
            添加第二位辅导老师
          </el-button>
          <div class="tutor-dialog-buttons">
            <el-button @click="tutorDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmTutors">确 定</el-button>
          </div>
        </div>
      </el-form>
    </el-dialog>

    <!-- 辅导老师详情对话框 -->
    <el-dialog
      title="辅导老师详情"
      :visible.sync="tutorDetailVisible"
      width="500px"
      append-to-body
    >
      <el-tabs v-model="activeTutorDetailTab" type="card">
        <el-tab-pane
          v-for="(tutor, index) in tutorDetails"
          :key="index"
          :label="'辅导老师' + (index + 1)"
          :name="'tutorDetail' + index"
        >
          <div class="tutor-detail-item">
            <span class="label">姓名：</span>
            <span class="value">{{ tutor.name }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">性别：</span>
            <span class="value">{{ tutor.gender }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">联系方式：</span>
            <span class="value">{{ tutor.contact }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">邮箱：</span>
            <span class="value">{{ tutor.email }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">职务/职称：</span>
            <span class="value">{{ tutor.title }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">工作单位：</span>
            <span class="value">{{ tutor.workUnit }}</span>
          </div>
          <div class="tutor-detail-item">
            <span class="label">所属地区：</span>
            <span class="value">{{ tutor.districtText }}</span>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 分数详情对话框 -->
    <el-dialog
      title="分数详情"
      :visible.sync="scoreDetailVisible"
      width="600px"
      append-to-body
    >
      <div class="score-detail-container">
        <div class="score-item" v-if="scoreDetailData.judge1Id">
          <h4>评分1</h4>
          <div class="judge-info">
            <p><strong>分数：</strong>{{ scoreDetailData.score1 || '-' }}</p>
            <p><strong>评语：</strong>{{ scoreDetailData.comment1 || '无' }}</p>
            <p><strong>评委：</strong>{{ scoreDetailData.judge1Name || '未知' }}</p>
          </div>
        </div>
        <div class="score-item" v-if="scoreDetailData.judge2Id">
          <h4>评分2</h4>
          <div class="judge-info">
            <p><strong>分数：</strong>{{ scoreDetailData.score2 || '-' }}</p>
            <p><strong>评语：</strong>{{ scoreDetailData.comment2 || '无' }}</p>
            <p><strong>评委：</strong>{{ scoreDetailData.judge2Name || '未知' }}</p>
          </div>
        </div>
        <div class="score-item" v-if="scoreDetailData.judge3Id">
          <h4>评分3</h4>
          <div class="judge-info">
            <p><strong>分数：</strong>{{ scoreDetailData.score3 || '-' }}</p>
            <p><strong>评语：</strong>{{ scoreDetailData.comment3 || '无' }}</p>
            <p><strong>评委：</strong>{{ scoreDetailData.judge3Name || '未知' }}</p>
          </div>
        </div>
        <div class="average-score">
          <h4>平均分：{{ scoreDetailData.averageScore || '-' }}</h4>
        </div>
      </div>
    </el-dialog>

    <!-- 评分对话框 -->
    <el-dialog
      title="评分"
      :visible.sync="judgeScoreVisible"
      width="450px"
      append-to-body
    >
      <div class="judge-score-tip">
        <el-alert
          title="注意：只能评分一次，一旦确定无法修改！"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
      <el-form
        ref="judgeScoreForm"
        :model="judgeScoreForm"
        :rules="judgeScoreRules"
        label-width="80px"
        style="margin-top: 20px;"
      >
        <el-form-item label="分数" prop="score">
          <el-input-number
            v-model="judgeScoreForm.score"
            :min="0"
            :max="100"
            :precision="1"
            :step="0.1"
            placeholder="请为该作品评分"
            style="width: 100%"
          />
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            分数范围：0-100分（可输入小数，如95.5分）
          </div>
        </el-form-item>
        <el-form-item label="评语" prop="comment">
          <div class="comment-input-container">
            <el-input
              v-model="judgeScoreForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入评语"
              maxlength="500"
              show-word-limit
            />
            <div class="comment-template-btn">
              <el-button
                type="text"
                size="mini"
                @click="openCommentTemplateDialog"
                icon="el-icon-document"
              >
                选择评语模板
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="judgeScoreVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitJudgeScore">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 评语模板选择对话框 -->
    <el-dialog
      title="选择评语模板"
      :visible.sync="commentTemplateDialogVisible"
      width="600px"
      append-to-body
    >
      <div class="template-dialog-header">
        <span>请选择合适的评语模板，点击即可应用到评语输入框：</span>
        <el-button
          type="primary"
          size="mini"
          @click="refreshCommentTemplates"
          icon="el-icon-refresh"
          :loading="templateLoading"
        >
          刷新模板
        </el-button>
      </div>

      <div class="template-dialog-content" v-loading="templateLoading">
        <div v-if="Object.keys(defaultComments).length === 0" class="no-template">
          <el-empty description="暂无评语模板">
            <el-button type="primary" @click="refreshCommentTemplates">加载模板</el-button>
          </el-empty>
        </div>
        <div v-else class="template-cards">
          <div
            v-for="(comment, key) in defaultComments"
            :key="key"
            class="template-card"
            @click="selectCommentTemplate(comment)"
          >
            <div class="template-card-header">
              <span class="template-title">模板 {{ key.replace('defaultComment', '') }}</span>
              <el-button type="text" size="mini" icon="el-icon-check">选择</el-button>
            </div>
            <div class="template-card-content">
              {{ comment }}
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="commentTemplateDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 异步导出进度对话框 -->
    <el-dialog
      title="导出进度"
      :visible.sync="exportProgressVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="export-progress-container">
        <div class="export-status-info">
          <div class="status-item">
            <span class="label">任务状态：</span>
            <span class="value">
              <el-tag :type="getExportStatusType()">{{ getExportStatusText() }}</el-tag>
            </span>
          </div>
          <div class="status-item" v-if="exportTaskId">
            <span class="label">任务ID：</span>
            <span class="value">{{ exportTaskId }}</span>
          </div>
        </div>

        <div class="export-progress-bar">
          <el-progress
            :percentage="exportProgress"
            :status="exportStatus === 'failed' ? 'exception' : (exportStatus === 'completed' ? 'success' : null)"
            :stroke-width="20"
          ></el-progress>
        </div>

        <div class="export-message">
          <i :class="getExportIconClass()"></i>
          <span>{{ exportMessage }}</span>
        </div>

        <div class="export-actions" v-if="exportStatus === 'completed'">
          <el-button type="primary" @click="downloadExportFile" icon="el-icon-download">
            下载文件
          </el-button>
        </div>

        <div class="export-tips">
          <el-alert
            title="提示：您可以关闭此对话框，导出将在后台继续进行"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeExportProgress" v-if="exportStatus !== 'processing'">
          {{ exportStatus === 'completed' ? '关闭' : '取消' }}
        </el-button>
        <el-button @click="exportProgressVisible = false" v-if="exportStatus === 'processing'">
          后台运行
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiAddProject, apiAuditProject, apiDeclarationProject,
  apiDeleteProject, apiDownDeclaration,
  apiDownloadTemplate,
  apiGetProjectDetail,
  apiGetProjectList,
  apiUpdateProject,
  apiDownByProjectId,
  apiRecommendProject,
  apiExportProjects,
  apiExportAllAsync,
  apiGetExportStatus,
  apiDownloadExportFile,
  apiGetExportHistory,
  apiGetProjectScore,
  apiSubmitScore,
  apiGetDefaultComment,
  apiCheckJudgeScored
} from "@/api/projectManage";
import {apiGetAreaTreeList} from "@/api/system/user";
import {getInfoLatest} from "@/api/login";
import Cookies from "js-cookie";
import ChunkedUpload from "@/components/ChunkedUpload";

export default {
  name: "Post",
  dicts: ["work_type", "grade", "article_status", "district_audit_status", "city_audit_status"],
  components: {
    ChunkedUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 作品表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      scoreOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        category: undefined,
        name: undefined,
        userName: undefined,
        district: undefined,
        participantType: undefined,
        auditStatus: undefined,
        articleStatus: undefined,
        sortField: undefined,
        sortOrder: undefined,
      },
      // 表单参数
      form: {
        category: undefined,
        descriptionText: '',
        tutors: [], // 添加辅导老师数组
      },
      form1: {
        score: undefined,
      },
      // 表单校验
      rules: {
        district: [
          { required: true, message: "所属区域不能为空", trigger: "blur" }
        ],
        name: [
          {required: true, message: "作品名称不能为空", trigger: "blur"},
        ],
        participantType: [
          {required: true, message: "活动对象不能为空", trigger: "blur"},
        ],
        category: [
          {required: true, message: "作品类别不能为空", trigger: "blur"},
        ],
        participantCount: [
          {required: true, message: "参与人数未选择", trigger: "blur"},
        ],  descriptionText: [
          {required: true, message: "所属区域不能为空", trigger: "blur"},
        ],  description: [
          {required: true, message: "附件不能为空", trigger: "blur"},
        ],  creatorStatement: [
          {required: true, message: "必填", trigger: "blur"},
        ],
        creationFile: [
          {required: true, message: "必填", trigger: "blur"},
        ],
        workUrl: [
          {required: true, message: "必填", trigger: "blur"},
        ],
        tutors: [
          { type: 'array', required: true, message: '请至少添加一位辅导老师', trigger: 'change' }
        ],
        // 删除原有的辅导老师和联系方式验证规则
        // ... existing code ...
      },
      rules1: {
        score: [{required: true, message: "评分不能为空", trigger: "blur"}],
      },
      addressOptions: [],
      handleType: "",
      userInfo: {},

      // 申报表
      applyOpen: false,
      currentProject: null,
      applyForm: {
        declarationUrl: '',
      },
      applyRules: {
        declarationUrl: [
          {required: true, message: '请上传申报表文件', trigger: 'blur'}
        ]
      },

      // 审核
      auditOpen: false,
      auditForm: {
        id: undefined,
        auditStatus: undefined,
        auditRemark: ''
      },
      auditRules: {
        auditStatus: [
          {required: true, message: '请选择审核状态', trigger: 'blur'}
        ]
      },

      // 推荐
      recommendOpen: false,
      recommendForm: {
        ids: [],
        recommendStatus: undefined,
        recommendRemark: ''
      },
      recommendRules: {
        recommendStatus: [
          {required: true, message: '请选择推荐状态', trigger: 'blur'}
        ]
      },

      // 区级审核选项
      // districtAuditOptions: [
      //   { value: 1, label: '初审通过', listClass: 'success' },
      //   { value: 2, label: '初审不通过', listClass: 'danger' }
      // ],

      // 市级审核选项
      // cityAuditOptions: [
      //   { value: 1, label: '终审通过', listClass: 'success' },
      //   { value: 2, label: '终审不通过', listClass: 'danger' }
      // ],

      // 区级审核详情
      districtAuditDetailOpen: false,
      districtAuditDetail: {},

      // 市级审核详情
      cityAuditDetailOpen: false,
      cityAuditDetail: {},

      // 固定滚动条
      showFixedScroll: false,
      tableWidth: 0,

      // 辅导老师对话框
      tutorDialogVisible: false,
      tutorDialogTitle: '辅导老师信息',
      activeTutorTab: 'tutor0',
      tutorForm: {
        tutors: []
      },
      tutorRules: {
        name: [
          { required: true, message: '请输入辅导老师姓名', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择辅导老师性别', trigger: 'change' }
        ],
        contact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入职务/职称', trigger: 'blur' }
        ],
        workUnit: [
          { required: true, message: '请输入工作单位', trigger: 'blur' }
        ],
        district: [
          { required: true, message: '请选择所属地区', trigger: 'change' }
        ]
      },

      // 辅导老师详情
      tutorDetailVisible: false,
      activeTutorDetailTab: 'tutorDetail0',
      tutorDetails: [],

      // 分数详情对话框
      scoreDetailVisible: false,
      scoreDetailData: {},

      // 评分对话框
      judgeScoreVisible: false,
      judgeScoreForm: {
        projectId: undefined,
        score: undefined,
        comment: ''
      },
      judgeScoreRules: {
        score: [
          { required: true, message: '请输入分数', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '分数必须在0-100之间', trigger: 'blur' }
        ]
      },

      // 默认评语
      defaultComments: {},
      commentTemplateDialogVisible: false,
      templateLoading: false,

      // 评分状态缓存
      scoredStatusCache: {},

      // 异步导出相关
      exportProgressVisible: false,
      exportTaskId: null,
      exportStatus: 'pending', // pending, processing, completed, failed
      exportProgress: 0,
      exportMessage: '正在准备导出...',
      exportPollingTimer: null,
      exportButtonDisabled: false,
      exportButtonCountdown: 0,
      exportCountdownTimer: null,
      exportHistory: [],

      // 响应式操作栏相关
      actionVisibility: {}, // 存储每行的按钮可见性状态
      resizeObserver: null, // ResizeObserver实例

    };
  },
  computed: {
    // 获取用户角色
    userRoles() {
      return this.$store.getters.roles || [];
    },
    // 判断是否为普通用户
    isCommonUser() {
      return this.userRoles.some(role => role.includes('common'));
    },
    // 判断是否为评委
    isJudge() {
      return this.userRoles.some(role => role.includes('judge'));
    },
    // 判断是否为区管理员
    isDistrictAdmin() {
      return this.userRoles.some(role => role.includes('district_'));
    },
    // 过滤的作品类型
    filteredWorkTypes() {
      const grade = this.userInfo.grade;
      console.log("grade === ", grade)
      if (!grade) return this.dict.type.work_type || [];

      const allTypes = this.dict.type.work_type || [];
      console.log("allTypes === ", allTypes)

      // 根据年级过滤可选的类别
      switch (grade) {
        case "1":
          return allTypes.filter(dict => dict.value === "1");
        case "2":
          return allTypes.filter(dict => ["2", "3"].includes(dict.value));
        case "3":
          return allTypes.filter(dict => ["4"].includes(dict.value));
        case "4":
          return allTypes.filter(dict => ["5"].includes(dict.value));
        case "5":
          return allTypes.filter(dict => dict.value === "5");
        default:
          return allTypes;
      }
    },
    // 参与人数选项
    participantCountOptions() {
      const grade = this.userInfo.grade;
      if (grade === "4") {
        // 教师组：1-5人
        return [1, 2, 3, 4, 5];
      } else {
        // 学生组：1-3人
        return [1, 2, 3];
      }
    },
    // 是否禁用新增按钮
    isAddButtonDisabled() {
      // 获取当前用户的角色
      const userRoles = this.$store.getters.roles || [];

      // 判断是否为普通角色（roleKey等于"common"）
      const isCommonRole = userRoles.includes('common');

      // 判断列表数据是否有1条或大于1条
      const hasProjectData = this.projectList && this.projectList.length >= 1;

      // 当用户是普通角色且有项目数据时，禁用新增按钮
      return isCommonRole && hasProjectData;
    },
    // 文件类型
    fileType() {
      let fileTypeMap = {
        1: ["png", "jpg", "jpeg"],
        3: ["doc", "docx"],
        5: ["doc", "docx"],
        4: ["pdf"],
      };
      return fileTypeMap[this.form.category];
    }
  },
  created() {
    this.getUserInfo();
    this.laodAddressOptionsa();
    this.getList();

    // 检查是否有未完成的导出任务
    this.checkUnfinishedExportTask();

    // this.getUserInfo().then(() => {
    //   if (!this.userInfo.IDNumber) {
    //     this.$router.push('/user/profile');
    //     this.$message.warning('请先完善个人信息');
    //   } else {
    //     this.getList();
    //   }
    // });
  },
  mounted() {
    this.$nextTick(() => {
      this.initFixedScroll();
      this.setScrollbarVar();
      this.initResponsiveActions(); // 初始化响应式操作栏
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    // 清理ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
    // 清理导出相关定时器
    this.stopPollingExportStatus();
    this.resetExportCooldown();
  },
  methods: {
    // 响应式操作栏相关方法

    // 初始化响应式操作栏
    initResponsiveActions() {
      this.$nextTick(() => {
        this.updateActionVisibility();
        this.setupResizeObserver();
      });
    },

    // 设置ResizeObserver监听表格容器大小变化
    setupResizeObserver() {
      if (typeof ResizeObserver !== 'undefined') {
        this.resizeObserver = new ResizeObserver(() => {
          this.updateActionVisibility();
        });

        const tableElement = this.$el.querySelector('.el-table');
        if (tableElement) {
          this.resizeObserver.observe(tableElement);
        }
      }
    },

    // 更新操作按钮的可见性
    updateActionVisibility() {
      this.$nextTick(() => {
        const containers = this.$el.querySelectorAll('.action-buttons-container');
        containers.forEach((container, index) => {
          this.calculateButtonVisibility(container, index);
        });
      });
    },

    // 计算单行操作按钮的可见性
    calculateButtonVisibility(container, rowIndex) {
      if (!container) return;

      const containerWidth = container.offsetWidth;
      const buttons = container.querySelectorAll('.action-btn');
      const moreButton = container.querySelector('.more-actions-dropdown');

      // 预留更多按钮的宽度（约50px）
      const moreButtonWidth = 50;
      let totalWidth = 0;
      let visibleCount = 0;

      // 详情按钮始终显示
      if (buttons.length > 0) {
        totalWidth += buttons[0].offsetWidth || 60; // 预估宽度
        visibleCount = 1;
      }

      // 计算其他按钮是否可以显示
      for (let i = 1; i < buttons.length; i++) {
        const buttonWidth = buttons[i].offsetWidth || 60; // 预估宽度
        const needMoreButton = i < buttons.length - 1; // 是否还有更多按钮
        const requiredWidth = totalWidth + buttonWidth + (needMoreButton ? moreButtonWidth : 0);

        if (requiredWidth <= containerWidth) {
          totalWidth += buttonWidth;
          visibleCount++;
        } else {
          break;
        }
      }

      // 更新可见性状态
      this.$set(this.actionVisibility, rowIndex, {
        visibleCount: visibleCount,
        hasHidden: visibleCount < buttons.length
      });
    },

    // 判断指定位置的按钮是否应该隐藏
    shouldHideAction(rowIndex, buttonIndex) {
      const visibility = this.actionVisibility[rowIndex];
      if (!visibility) return false;

      // 详情按钮（索引0）始终显示
      if (buttonIndex === 0) return false;

      return buttonIndex >= visibility.visibleCount;
    },

    // 判断是否有隐藏的操作按钮
    hasHiddenActions(rowIndex) {
      const visibility = this.actionVisibility[rowIndex];
      return visibility && visibility.hasHidden;
    },

    // 处理更多操作下拉菜单的命令
    handleMoreCommand(command, row) {
      switch (command) {
        case 'update':
          this.handleUpdate(row);
          break;
        case 'score':
          this.handleScoreAction(row);
          break;
        case 'apply':
          this.handleApply(row);
          break;
        case 'audit':
          this.handleAudit(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
        default:
          console.warn('Unknown command:', command);
      }
    },

    // 处理窗口大小变化
    handleResize() {
      this.setTableHeight();
      this.setScrollbarVar();
      this.updateActionVisibility(); // 添加响应式操作栏更新
    },

    // 计算并写入滚动条厚度 CSS 变量，保证固定列与主体底部对齐
    setScrollbarVar() {
      this.$nextTick(() => {
        const wrap = this.$el && this.$el.querySelector('.el-table__body-wrapper');
        if (!wrap) return;
        const sb = Math.max(0, wrap.offsetHeight - wrap.clientHeight);
        // 写入到根变量，样式中通过 var(--pm-scrollbar) 读取
        document.documentElement.style.setProperty('--pm-scrollbar', `${sb}px`);
      });
    },

    // 获取分数按钮文本
    getScoreButtonText() {
      if (this.isCommonUser || this.isDistrictAdmin) {
        return '分数';
      } else {
        return '评分';
      }
    },

    // 判断评分按钮是否应该禁用
    isScoreButtonDisabled(row) {
      // 如果是普通用户或区管理员（显示"分数"），不置灰
      if (this.isCommonUser || this.isDistrictAdmin) {
        return false;
      }

      // 如果平均分有值，说明三个评委都评过了，按钮置灰
      if (row.averageScore !== null && row.averageScore !== undefined && row.averageScore !== '') {
        return true;
      }

      // 只有评委角色才需要检查是否已评分
      if (this.isJudge) {
        return this.scoredStatusCache[row.id] === true;
      }
      return false;
    },

    // 获取评分按钮的提示文本
    getScoreButtonTooltip(row) {
      // 如果是普通用户或区管理员（显示"分数"），不显示提示
      if (this.isCommonUser || this.isDistrictAdmin) {
        return '';
      }

      // 如果平均分有值，说明三个评委都评过了
      if (row.averageScore !== null && row.averageScore !== undefined && row.averageScore !== '') {
        return '该作品已完成评分';
      }

      // 如果是评委且已评分过
      if (this.isJudge && this.scoredStatusCache[row.id] === true) {
        return '该作品您已评分过';
      }
      return '';
    },

    // 处理分数/评分按钮点击
    handleScoreAction(row) {
      if (this.isCommonUser || this.isDistrictAdmin) {
        // 普通用户和区管理员查看分数详情
        this.showScoreDetail(row);
      } else {
        // 其他角色进行评分
        this.showJudgeScore(row);
      }
    },

    // 显示分数详情
    async showScoreDetail(row) {
      try {
        const response = await apiGetProjectScore(row.id);
        if (response.code === 200) {
          this.scoreDetailData = response.data || {};
          this.scoreDetailVisible = true;
        } else {
          this.$message.error(response.msg || '获取分数信息失败');
        }
      } catch (error) {
        console.error('获取分数信息错误:', error);
        this.$message.error('获取分数信息失败');
      }
    },

    // 显示评分对话框
    showJudgeScore(row) {
      this.judgeScoreForm = {
        projectId: row.id,
        score: undefined,
        comment: ''
      };
      this.judgeScoreVisible = true;
    },

    // 打开评语模板对话框
    async openCommentTemplateDialog() {
      this.commentTemplateDialogVisible = true;
      // 如果还没有加载默认评语，则自动加载
      if (Object.keys(this.defaultComments).length === 0) {
        await this.loadCommentTemplates();
      }
    },

    // 加载评语模板
    async loadCommentTemplates() {
      try {
        this.templateLoading = true;
        const response = await apiGetDefaultComment();
        if (response.code === 200) {
          this.defaultComments = response.data || {};
        } else {
          this.$message.error(response.msg || '获取评语模板失败');
        }
      } catch (error) {
        console.error('获取评语模板错误:', error);
        this.$message.error('获取评语模板失败');
      } finally {
        this.templateLoading = false;
      }
    },

    // 刷新评语模板
    async refreshCommentTemplates() {
      await this.loadCommentTemplates();
      if (Object.keys(this.defaultComments).length > 0) {
        this.$message.success('评语模板已更新');
      }
    },

    // 选择评语模板
    selectCommentTemplate(comment) {
      this.judgeScoreForm.comment = comment;
      this.commentTemplateDialogVisible = false;
      this.$message.success('已选择评语模板');
    },

    // 提交评分
    async submitJudgeScore() {
      try {
        const valid = await this.$refs['judgeScoreForm'].validate();
        if (valid) {
          const response = await apiSubmitScore({
            projectId: this.judgeScoreForm.projectId,
            score: this.judgeScoreForm.score,
            comment: this.judgeScoreForm.comment
          });

          if (response.code === 200) {
            this.$message.success('评分成功');
            this.judgeScoreVisible = false;
            // 清除该项目的评分状态缓存
            this.scoredStatusCache[this.judgeScoreForm.projectId] = true;
            this.getList(); // 刷新列表
          } else {
            this.$message.error(response.msg || '评分失败');
          }
        }
      } catch (error) {
        console.error('提交评分错误:', error);
        this.$message.error('提交评分失败');
      }
    },

    // 处理排序变化
    handleSortChange({ column, prop, order }) {
      if (prop === 'averageScore') {
        this.queryParams.sortField = 'averageScore';
        this.queryParams.sortOrder = order === 'ascending' ? 'asc' : 'desc';
        this.getList();
      }
    },

    // 检查评分状态
    async checkScoredStatus(projectId) {
      // 只有评委角色才需要检查
      if (!this.isJudge) {
        return false;
      }

      // 如果缓存中已有结果，直接返回
      if (this.scoredStatusCache.hasOwnProperty(projectId)) {
        return this.scoredStatusCache[projectId];
      }

      try {
        const response = await apiCheckJudgeScored(projectId);
        if (response.code === 200) {
          this.scoredStatusCache[projectId] = response.data;
          return response.data;
        }
      } catch (error) {
        console.error('检查评分状态错误:', error);
      }

      return false;
    },

    // 批量检查评分状态
    async batchCheckScoredStatus() {
      if (!this.isJudge || !this.projectList.length) {
        return;
      }

      const promises = this.projectList.map(project =>
        this.checkScoredStatus(project.id)
      );

      try {
        await Promise.all(promises);
        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        console.error('批量检查评分状态错误:', error);
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getInfoLatest();
        console.log("获取用户信息成功：", response)
        this.userInfo = response.user || {};
        if (this.userInfo.grade) {
          this.form.participantType = this.userInfo.grade;
        } else {
          this.form.participantType = "5";
        }
        return Promise.resolve();
      } catch (error) {
        console.error("获取用户信息错误：", error);
        return Promise.reject(error);
      }
    },

    /** 查询用户列表 */
    getList() {
      this.loading = true;
      const username = Cookies.get('username');

      if (username) {
        this.queryParams.userName = username;
        this.fetchProjectList();
      } else {
        // 如果 cookie 中没有 username，调用 getUserInfo 获取用户信息
        this.getUserInfo().then(() => {
          // 用户信息获取成功后，再次尝试获取 userName
          const newUsername = this.userInfo?.userName || null;
          if (newUsername) {
            this.queryParams.userName = newUsername;
            this.fetchProjectList();
          } else {
            this.$message.error('无法获取用户信息，请重新登录');
            this.loading = false;
          }
        }).catch(() => {
          this.$message.error('获取用户信息失败，请重新登录');
          this.loading = false;
        });
      }
    },

    // 封装获取项目列表的逻辑
    fetchProjectList() {
      // 处理district参数，如果存在则转换为字符串
      const params = {...this.queryParams};
      if (params.district && Array.isArray(params.district) && params.district.length > 0) {
        params.district = params.district.join(',');
      }

      apiGetProjectList(params).then((response) => {
        this.projectList = response.data?.list || [];
        this.total = response.data?.total ?? 0;
        this.loading = false;

        // 数据加载完成后初始化滚动条
        this.$nextTick(() => {
          this.initFixedScroll();
          this.setTableHeight(); // 重新计算表格高度
          // 如果是评委角色，检查评分状态
          this.batchCheckScoredStatus();
          // 更新响应式操作栏
          this.updateActionVisibility();
        });
      }).catch(() => {
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      const participantType = this.form.participantType;
      this.form = {
        district: [],
        category: undefined,
        participantType: participantType,
        participantCount: 1,
        description: undefined,
        name: undefined,
        tutors: [], // 重置辅导老师数组
        creationFile: undefined,
        creatorStatement: undefined,
        workUrl: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    async handleAdd() {
      await this.laodAddressOptionsa();
      await this.getUserInfo();
      if (!this.userInfo.IDNumber) {
        this.$router.push('/user/profile');
        this.$message.warning('请先完善个人信息');
        return;
      }

      this.reset();
      this.handleType = "add";
      this.open = true;
      this.title = "新增项目";
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      this.handleType = "edit";
      await this.laodAddressOptionsa();
      const id = row.id;
      apiGetProjectDetail(id).then((response) => {
        // 打印从后端获取的原始数据
        console.log('从后端获取的原始数据:', response.data);

        this.form = {
          ...response.data,
          district:
            response.data?.district && response.data.district.split(","),
        };

        // 确保category是字符串类型的数字
        if (this.form.category) {
          console.log('修改前的category值:', this.form.category);
          // 如果category不是字符串类型的数字，尝试通过字典找到对应的值
          if (isNaN(this.form.category)) {
            const foundDict = this.dict.type.work_type.find(dict => dict.label === this.form.category);
            if (foundDict) {
              this.form.category = foundDict.value;
              console.log('修改后的category值:', this.form.category);
            }
          }
        }

        // 处理辅导老师信息
        if (response.data.tutors && response.data.tutors.length > 0) {
          // 保持district字段原样，它应该是区域代码的逗号分隔字符串
          this.form.tutors = response.data.tutors;
        } else if (response.data.tutorName) {
          // 向后兼容，如果没有tutors数组但有tutorName和tutorContact
          this.form.tutors = [{
            name: response.data.tutorName || '',
            contact: response.data.tutorContact || '',
            gender: '',
            email: '',
            title: '',
            workUnit: '',
            district: response.data.district || '', // 使用项目的district
            districtText: response.data.addressContent || ''
          }];
        } else {
          this.form.tutors = [];
        }

        this.open = true;
        this.title = "修改项目";
      });
    },

    async handleDetail(row) {
      this.reset();
      this.handleType = "detail";
      await this.laodAddressOptionsa();
      const id = row.id;
      apiGetProjectDetail(id).then((response) => {
        this.form = {
          ...response.data,
          district:
            response.data?.district && response.data.district.split(","),
        };

        // 处理辅导老师信息
        if (response.data.tutors && response.data.tutors.length > 0) {
          // 保持district字段原样，它应该是区域代码的逗号分隔字符串
          this.form.tutors = response.data.tutors;
        } else if (response.data.tutorName) {
          // 向后兼容，如果没有tutors数组但有tutorName和tutorContact
          this.form.tutors = [{
            name: response.data.tutorName || '',
            contact: response.data.tutorContact || '',
            gender: '',
            email: '',
            title: '',
            workUnit: '',
            district: response.data.district || '', // 使用项目的district
            districtText: response.data.addressContent || ''
          }];
        } else {
          this.form.tutors = [];
        }

        this.open = true;
        this.title = "查看";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const {district} = this.form;

          // 检查是否至少有一位辅导老师
          if (!this.form.tutors || this.form.tutors.length === 0) {
            this.$message.error('请至少添加一位辅导老师');
            return;
          }

          // 处理辅导老师信息，保持向后兼容
          const tutorName = this.form.tutors[0]?.name || '';
          const tutorContact = this.form.tutors[0]?.contact || '';

          // 确保辅导老师的district是字符串格式（区域代码的逗号分隔字符串）
          const tutors = this.form.tutors.map(tutor => {
            return {
              ...tutor,
              district: typeof tutor.district === 'string' ? tutor.district :
                (Array.isArray(tutor.district) ? tutor.district.join(',') : '')
            };
          });

          let params = {
            ...this.form,
            district: district.length > 0 ? district.join(",") : undefined,
            userName: this.userInfo.userName || '',
            tutorName: tutorName,
            tutorContact: tutorContact,
            tutors: tutors
          };

          // 添加日志，查看实际提交的数据
          console.log('提交的表单数据:', params);

          if (this.form.id != undefined) {
            apiUpdateProject(params).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            apiAddProject(params).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleSetScore(row) {
      this.form1.id = row.id;
      this.scoreOpen = true;
      this.form1.score = undefined;
    },
    handleSaveScore() {
      this.$refs["form1"].validate((valid) => {
        if (valid) {
          let params = {
            ...this.form1,
            score: this.form1.score,
          };
          apiUpdateProject(params).then((response) => {
            this.$modal.msgSuccess("成功");
            this.scoreOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id;
      const $this = this;
      this.$modal.confirm("是否确认删除？").then(async function () {
        const {code} = await apiDeleteProject(postIds);
        if (code === 200) {
          $this.getList();
          $this.$modal.msgSuccess("删除成功");
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.ids.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }

      this.$modal.confirm('确认导出所选作品?').then(() => {
        this.loading = true;
        apiExportProjects(this.ids).then(response => {
          // 创建blob对象
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });
          const fileName = `作品导出_${new Date().getTime()}.xlsx`;

          // 创建下载链接
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();

          // 释放URL对象
          window.URL.revokeObjectURL(link.href);
          this.loading = false;
          this.$modal.msgSuccess("导出成功");
        }).catch(() => {
          this.loading = false;
          this.$modal.msgError("导出失败");
        });
      }).catch(() => {});
    },

    /** 导出全部按钮操作 */
    handleExportAll() {
      // 检查是否在冷却期内
      if (this.exportButtonDisabled) {
        this.$message.warning(`请等待 ${this.exportButtonCountdown} 秒后再试`);
        return;
      }

      // 检查是否有正在进行的导出任务
      const savedTaskId = localStorage.getItem('exportTaskId');
      if (savedTaskId) {
        this.$modal.confirm('检测到有未完成的导出任务，是否查看进度？')
          .then(() => {
            this.exportTaskId = savedTaskId;
            this.checkExportStatus();
            this.exportProgressVisible = true;
          })
          .catch(() => {
            // 用户选择不查看，清除保存的任务ID
            localStorage.removeItem('exportTaskId');
          });
        return;
      }

      this.$modal.confirm('确认导出全部作品？导出可能需要几分钟时间，您可以在后台继续其他操作。')
        .then(() => {
          this.startAsyncExport();
        })
        .catch(() => {});
    },

    /** 启动异步导出 */
    async startAsyncExport() {
      try {
        // 启动按钮冷却
        this.startExportCooldown();

        // 调用异步导出接口
        const response = await apiExportAllAsync();

        if (response.code === 200) {
          this.exportTaskId = response.data.taskId;
          this.exportStatus = 'processing';
          this.exportProgress = 0;
          this.exportMessage = '导出任务已启动，正在处理中...';
          this.exportProgressVisible = true;

          // 保存任务ID到本地存储
          localStorage.setItem('exportTaskId', this.exportTaskId);

          // 开始轮询任务状态
          this.startPollingExportStatus();

          this.$message.success('导出任务已启动');
        } else {
          this.$message.error(response.msg || '启动导出任务失败');
          this.resetExportCooldown();
        }
      } catch (error) {
        console.error('启动异步导出失败:', error);
        this.$message.error('启动导出任务失败');
        this.resetExportCooldown();
      }
    },

    /** 开始导出按钮冷却 */
    startExportCooldown() {
      this.exportButtonDisabled = true;
      this.exportButtonCountdown = 300; // 5分钟 = 300秒

      this.exportCountdownTimer = setInterval(() => {
        this.exportButtonCountdown--;
        if (this.exportButtonCountdown <= 0) {
          this.resetExportCooldown();
        }
      }, 1000);
    },

    /** 重置导出按钮冷却 */
    resetExportCooldown() {
      this.exportButtonDisabled = false;
      this.exportButtonCountdown = 0;
      if (this.exportCountdownTimer) {
        clearInterval(this.exportCountdownTimer);
        this.exportCountdownTimer = null;
      }
    },

    /** 开始轮询导出状态 */
    startPollingExportStatus() {
      this.exportPollingTimer = setInterval(() => {
        this.checkExportStatus();
      }, 2000); // 每2秒检查一次
    },

    /** 停止轮询导出状态 */
    stopPollingExportStatus() {
      if (this.exportPollingTimer) {
        clearInterval(this.exportPollingTimer);
        this.exportPollingTimer = null;
      }
    },

    /** 检查导出状态 */
    async checkExportStatus() {
      if (!this.exportTaskId) return;

      try {
        const response = await apiGetExportStatus(this.exportTaskId);

        if (response.code === 200) {
          const data = response.data;
          this.exportStatus = data.status;
          this.exportProgress = data.progress || 0;
          this.exportMessage = data.message || '';

          if (data.status === 'completed') {
            this.exportProgress = 100;
            this.exportMessage = '导出完成！点击下载按钮获取文件。';
            this.stopPollingExportStatus();
            // 注意：不要在这里清除 localStorage，等用户下载完成后再清除

            // 显示成功通知
            this.$notify({
              title: '导出完成',
              message: '作品导出已完成，点击此通知打开下载对话框',
              type: 'success',
              duration: 0, // 不自动关闭
              onClick: () => {
                console.log('通知被点击，任务ID:', this.exportTaskId);
                // 确保状态正确设置
                this.exportStatus = 'completed';
                this.exportProgress = 100;
                this.exportMessage = '导出完成！点击下载按钮获取文件。';
                this.exportProgressVisible = true;
                console.log('对话框应该已打开，状态:', this.exportStatus);
              }
            });
          } else if (data.status === 'failed') {
            this.exportMessage = data.message || '导出失败，请重试';
            this.stopPollingExportStatus();
            localStorage.removeItem('exportTaskId');
            this.resetExportCooldown(); // 失败时重置冷却

            this.$notify({
              title: '导出失败',
              message: this.exportMessage,
              type: 'error'
            });
          }
        }
      } catch (error) {
        console.error('检查导出状态失败:', error);
      }
    },

    /** 下载导出文件 */
    async downloadExportFile() {
      if (!this.exportTaskId) return;

      try {
        const response = await apiDownloadExportFile(this.exportTaskId);

        // 创建blob对象
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const fileName = `作品导出_${new Date().getTime()}.xlsx`;

        // 创建下载链接
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();

        // 释放URL对象
        window.URL.revokeObjectURL(link.href);

        // 下载成功后清除任务ID
        localStorage.removeItem('exportTaskId');

        this.$message.success('文件下载成功');
        this.exportProgressVisible = false;
      } catch (error) {
        console.error('下载文件失败:', error);
        this.$message.error('下载文件失败');
      }
    },

    /** 关闭导出进度对话框 */
    closeExportProgress() {
      this.exportProgressVisible = false;
      this.stopPollingExportStatus();

      if (this.exportStatus !== 'completed' && this.exportStatus !== 'failed') {
        // 如果任务还在进行中，保留任务ID
        if (this.exportTaskId) {
          localStorage.setItem('exportTaskId', this.exportTaskId);
        }
      } else {
        // 任务已完成或失败，清除任务ID
        localStorage.removeItem('exportTaskId');
      }
    },

    /** 获取导出状态类型 */
    getExportStatusType() {
      switch (this.exportStatus) {
        case 'completed': return 'success';
        case 'failed': return 'danger';
        case 'processing': return 'warning';
        default: return 'info';
      }
    },

    /** 获取导出状态文本 */
    getExportStatusText() {
      switch (this.exportStatus) {
        case 'pending': return '等待中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return '未知';
      }
    },

    /** 获取导出图标类 */
    getExportIconClass() {
      switch (this.exportStatus) {
        case 'processing': return 'el-icon-loading';
        case 'completed': return 'el-icon-success';
        case 'failed': return 'el-icon-error';
        default: return 'el-icon-info';
      }
    },

    /** 检查未完成的导出任务 */
    async checkUnfinishedExportTask() {
      const savedTaskId = localStorage.getItem('exportTaskId');
      if (savedTaskId) {
        this.exportTaskId = savedTaskId;
        try {
          await this.checkExportStatus();
          if (this.exportStatus === 'processing') {
            // 如果任务还在进行中，询问用户是否查看进度
            this.$notify({
              title: '检测到未完成的导出任务',
              message: '点击查看导出进度',
              type: 'info',
              duration: 5000,
              onClick: () => {
                this.exportProgressVisible = true;
                this.startPollingExportStatus();
              }
            });
          } else if (this.exportStatus === 'completed') {
            // 如果任务已完成，提示用户下载
            this.$notify({
              title: '检测到已完成的导出任务',
              message: '点击此通知下载文件',
              type: 'success',
              duration: 0, // 不自动关闭
              onClick: () => {
                console.log('检测到已完成任务的通知被点击，任务ID:', this.exportTaskId);
                console.log('当前导出状态:', this.exportStatus);
                this.exportProgressVisible = true;
              }
            });
          } else {
            // 任务失败，清除保存的任务ID
            localStorage.removeItem('exportTaskId');
          }
        } catch (error) {
          console.error('检查未完成导出任务失败:', error);
          localStorage.removeItem('exportTaskId');
        }
      }
    },

    async laodAddressOptionsa() {
      const {data, code} = await apiGetAreaTreeList();
      if (code !== 200) return;
      this.addressOptions = data;
    },
    handleOnAddressChange() {
      console.log(
        this.$refs.cascaderRef
          .getCheckedNodes()[0]
          ?.pathNodes.map((item) => item.label)
          .join("/")
      );
      this.form.addressContent = this.$refs.cascaderRef
        .getCheckedNodes()[0]
        ?.pathNodes.map((item) => item.label)
        .join("/");
    },
    handleChangeFile(value, props) {
      this.form[props] = value;
    },
    async importTemplate() {
      let idFlag = this.userInfo.grade === "4" ? "teacher" : "student";
      console.log("请求idFlag === ", idFlag);
      const {code, data} = await apiDownloadTemplate(idFlag);
      if (code == 200 && data) {
        const cleanedUrl = new URL(data, window.location.href).href;
        const a = document.createElement("a");
        a.href = cleanedUrl;
        a.target = "_blank";
        a.download = "创作者声明模版";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },

    // 处理申报按钮点击
    handleApply(row) {
      this.currentProject = row;
      this.applyForm.declarationUrl = '';
      this.applyOpen = true;
    },

    // 申报表文件上传处理
    handleChangeDeclarationFile(url) {
      this.applyForm.declarationUrl = url;
    },

    // 导出申报表
    async downloadDeclarationTemplate() {
      const projectId = this.currentProject.id;
      try {
        const response = await apiDownByProjectId(projectId); // 注意这里不要解构 { code, data }
        const blob = new Blob([response], { type: 'application/pdf' });
        const downloadUrl = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = '作品申报表.pdf'; // 设置下载文件名
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(downloadUrl); // 清理内存
      } catch (error) {
        console.error('文件下载失败:', error);
        this.$message.error('文件下载失败');
      }
    },

    // 提交申报
    async submitApply() {
      try {
        const valid = await this.$refs['applyForm'].validate();
        if (valid) {
          const params = {
            id: this.currentProject.id,
            declarationUrl: this.applyForm.declarationUrl
          };
          const {code, message} = await apiDeclarationProject(params);
          if (code === 200) {
            this.$modal.msgSuccess('申报成功');
            this.applyOpen = false;
            this.getList();
          } else {
            this.$modal.msgError(message || '申报失败');
          }
        }
      } catch (error) {
        this.$modal.msgError('提交申报时发生错误');
        console.error('提交申报错误:', error);
      }
    },

    // 审核
    // 处理审核按钮点击
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        auditStatus: undefined,
        auditRemark: ''
      };
      this.auditOpen = true;
    },

    // 提交审核
    async submitAudit() {
      try {
        const valid = await this.$refs['auditForm'].validate();
        if (valid) {
          const {code, message} = await apiAuditProject({
            ...this.auditForm,
            auditor: this.userInfo.userName
          });
          if (code === 200) {
            this.$modal.msgSuccess('审核成功');
            this.auditOpen = false;
            this.getList();
          } else {
            this.$modal.msgError(message || '审核失败');
          }
        }
      } catch (error) {
        this.$modal.msgError('提交审核时发生错误');
        console.error('提交审核错误:', error);
      }
    },

    // 处理作品简介文本输入
    handleDescriptionTextInput(value) {
      this.form.descriptionText = value;
    },

    // 处理推荐按钮点击
    handleRecommend() {
      if (this.ids.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }

      this.recommendForm = {
        ids: this.ids,
        recommendStatus: undefined,
        recommendRemark: ''
      };
      this.recommendOpen = true;
    },

    // 提交推荐
    async submitRecommend() {
      try {
        const valid = await this.$refs['recommendForm'].validate();
        if (valid) {
          const {code, message} = await apiRecommendProject({
            ...this.recommendForm,
            districtAuditor: this.userInfo.userName
          });
          if (code === 200) {
            this.$modal.msgSuccess('推荐成功');
            this.recommendOpen = false;
            this.getList();
          } else {
            this.$modal.msgError(message || '推荐失败');
          }
        }
      } catch (error) {
        this.$modal.msgError('提交推荐时发生错误');
        console.error('提交推荐错误:', error);
      }
    },

    // 显示区级审核详情
    showDistrictAuditDetail(row) {
      this.districtAuditDetail = {
        districtAuditStatus: row.districtAuditStatus,
        districtAuditor: row.districtAuditor,
        districtAuditRemark: row.districtAuditRemark
      };
      this.districtAuditDetailOpen = true;
    },

    // 显示市级审核详情
    showCityAuditDetail(row) {
      this.cityAuditDetail = {
        cityAuditStatus: row.auditStatus,
        cityAuditor: row.auditor,
        cityAuditRemark: row.auditRemark
      };
      this.cityAuditDetailOpen = true;
    },

    // 初始化固定滚动条
    initFixedScroll() {
      // 使用setTimeout确保表格完全渲染
      setTimeout(() => {
        const tableEl = this.$el.querySelector('.el-table__body');
        if (!tableEl) return;

        // 获取表格实际宽度
        this.tableWidth = tableEl.scrollWidth;

        // 判断是否需要显示滚动条
        const tableWrapper = this.$el.querySelector('.el-table__body-wrapper');
        if (!tableWrapper) return;

        // 设置表格高度以填充可用空间
        this.setTableHeight();
        // 同步滚动条厚度到 CSS 变量，确保固定列与主体对齐
        this.setScrollbarVar();

        // 原有的固定滚动条逻辑，现在默认隐藏
        this.showFixedScroll = false; // 默认不显示自定义滚动条，使用原生滚动条
      }, 200); // 延迟200毫秒确保表格渲染完成
    },

    // 设置表格高度以填充可用空间
    setTableHeight() {
      // 获取容器元素
      const appContainer = this.$el.querySelector('.app-container');
      if (!appContainer) return;

      // 获取其他元素高度
      const searchForm = this.$el.querySelector('.project-search-form');
      const toolbarRow = this.$el.querySelector('.mb8');
      const pagination = this.$el.querySelector('.pagination');

      // 计算其他元素的总高度
      const otherHeight =
        (searchForm ? searchForm.offsetHeight : 0) +
        (toolbarRow ? toolbarRow.offsetHeight : 0) +
        (pagination ? pagination.offsetHeight : 0) +
        40; // 额外边距

      // 获取视窗高度
      const viewportHeight = window.innerHeight;

      // 计算表格应该的高度
      const tableHeight = viewportHeight - otherHeight - 84; // 84是头部高度

      // 设置表格容器高度
      const tableContainer = this.$el.querySelector('.table-with-scroll');
      if (tableContainer) {
        tableContainer.style.height = `${tableHeight}px`;
      }
    },

    // 辅导老师相关方法
    showTutorDialog() {
      this.tutorForm.tutors = [];

      // 如果已有辅导老师信息，则复制到对话框中
      if (this.form.tutors && this.form.tutors.length > 0) {
        this.form.tutors.forEach(tutor => {
          // 复制辅导老师信息
          const tutorCopy = {...tutor};

          // 如果district是字符串，需要转换为数组以便级联选择器能正常工作
          if (typeof tutorCopy.district === 'string' && tutorCopy.district) {
            tutorCopy.district = tutorCopy.district.split(',');
          } else if (!tutorCopy.district) {
            tutorCopy.district = [];
          }

          this.tutorForm.tutors.push(tutorCopy);
        });
      } else {
        // 否则初始化第一个辅导老师
        this.tutorForm.tutors.push({
          name: '',
          gender: '',
          contact: '',
          email: '',
          title: '',
          workUnit: '',
          district: this.form.district ? [...this.form.district] : [], // 使用项目的district数组
          districtText: this.form.addressContent || ''
        });
      }

      this.activeTutorTab = 'tutor0';
      this.tutorDialogVisible = true;
    },

    addTutor() {
      if (this.tutorForm.tutors.length < 2) {
        this.tutorForm.tutors.push({
          name: '',
          gender: '',
          contact: '',
          email: '',
          title: '',
          workUnit: '',
          district: this.form.district ? [...this.form.district] : [], // 使用项目的district数组
          districtText: this.form.addressContent || ''
        });
        this.activeTutorTab = 'tutor' + (this.tutorForm.tutors.length - 1);
      }
    },

    removeTutor(index) {
      this.tutorForm.tutors.splice(index, 1);
      this.activeTutorTab = 'tutor0';
    },

    handleTutorDistrictChange(value, index) {
      if (value && value.length > 0) {
        // 获取选中的地区文本
        const cascader = this.$el.querySelector('.tutor-form .el-cascader');
        if (cascader) {
          const selectedNodes = cascader.__vue__.getCheckedNodes();
          if (selectedNodes && selectedNodes.length > 0) {
            const pathLabels = selectedNodes[0].pathLabels;
            const districtText = pathLabels.join('/');
            this.tutorForm.tutors[index].districtText = districtText;

            // 将district设置为区域代码的逗号分隔字符串，与项目所属区域参数保持一致
            this.tutorForm.tutors[index].district = value.join(',');
          }
        }
      } else {
        this.tutorForm.tutors[index].districtText = '';
        this.tutorForm.tutors[index].district = '';
      }
    },

    confirmTutors() {
      this.$refs.tutorForm.validate(valid => {
        if (valid) {
          // 至少需要一位辅导老师
          if (this.tutorForm.tutors.length === 0) {
            this.$message.error('请至少添加一位辅导老师');
            return;
          }

          // 处理每个辅导老师的地区信息
          const processedTutors = this.tutorForm.tutors.map(tutor => {
            // 创建一个新对象，避免修改原对象
            const newTutor = {...tutor};

            // 如果没有districtText但有district数组
            if (!newTutor.districtText && newTutor.district && Array.isArray(newTutor.district) && newTutor.district.length > 0) {
              // 尝试从addressOptions中获取地区文本
              const districtPath = [];
              let currentOptions = this.addressOptions.filter(opt => opt.id === '110000');

              for (let i = 0; i < newTutor.district.length; i++) {
                const id = newTutor.district[i];
                const option = currentOptions.find(opt => opt.id === id);

                if (option) {
                  districtPath.push(option.name);
                  currentOptions = option.children || [];
                }
              }

              newTutor.districtText = districtPath.join('/');

              // 将district设置为区域代码的逗号分隔字符串
              newTutor.district = newTutor.district.join(',');
            }

            // 如果district仍然是数组，转换为字符串
            if (Array.isArray(newTutor.district)) {
              newTutor.district = newTutor.district.join(',');
            }

            return newTutor;
          });

          // 更新form中的辅导老师信息
          this.form.tutors = processedTutors;

          // 关闭对话框
          this.tutorDialogVisible = false;
        }
      });
    },

    editTutor(index) {
      this.showTutorDialog();
      this.activeTutorTab = 'tutor' + index;
    },

    // 查看辅导老师详情
    viewTutorDetail(index) {
      if (this.form.tutors && this.form.tutors.length > 0) {
        this.tutorDetails = JSON.parse(JSON.stringify(this.form.tutors));
        // 如果指定了索引，则显示该索引的辅导老师详情
        this.activeTutorDetailTab = index !== undefined ? 'tutorDetail' + index : 'tutorDetail0';
        this.tutorDetailVisible = true;
      } else {
        this.$message.info('没有辅导老师信息');
      }
    },


  },

  watch: {
    // 监听字典数据加载完成
    'dict.type.work_type': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          // 确保字典数据加载完成后再设置默认值
          this.$nextTick(() => {
            // 根据用户年级设置默认值
            const grade = this.userInfo.grade;
            if (grade === "1") {
              this.form.category = "1"; // AI艺术生成
            } else if (["2", "3", "4"].includes(grade)) {
              this.form.category = "2"; // AI交互设计
            } else if (["5", "6"].includes(grade)) {
              this.form.category = "4"; // AI算法挑战
            } else if (grade === "7") {
              this.form.category = "5"; // AI创新教学案例征集
            } else {
              this.form.category = null;
            }
          });
        }
      }
    },
    'form.category': {
      handler(newVal) {
        if (!newVal) return;

        const isLimitedCategory = newVal === '1' || newVal === '5';
        if (isLimitedCategory && this.form.participantCount > 2) {
          this.form.participantCount = 1;
        } else if (!isLimitedCategory && this.form.participantCount > 3) {
          this.form.participantCount = 1;
        }
      },
      immediate: true
    },
    // 监听projectList变化，更新滚动条
    projectList: {
      handler() {
        this.$nextTick(() => {
          this.initFixedScroll();
        });
      },
      deep: true
    }
  },
};
</script>

<style>
/* 全局样式，不使用scoped */
.custom-dialog .el-dialog__body {
  padding: 20px 30px;
}

.custom-form-item {
  margin-bottom: 25px !important;
  position: relative;
  display: flex;
  align-items: center !important; /* 居中对齐 */
}

.custom-form-item .el-form-item__label {
  padding-bottom: 0 !important; /* 移除底部内边距 */
  white-space: nowrap !important;
  width: 100px !important;
  min-width: 100px !important;
  text-align: right !important;
  float: left;
  padding-right: 12px;
  box-sizing: border-box;
  line-height: 32px !important; /* 与单选按钮一致的行高 */
  height: 32px !important; /* 固定高度 */
}

.custom-form-item .el-form-item__content {
  margin-left: 0 !important;
  flex: 1;
  line-height: 32px !important; /* 与单选按钮一致的行高 */
}

.custom-radio {
  margin-right: 30px !important;
  line-height: 32px !important;
}

.custom-radio .el-radio__label {
  padding-left: 8px;
}

/* 修复标签宽度和对齐 */
.custom-dialog .el-form-item__label {
  width: 100px !important;
  white-space: nowrap !important;
  text-align: right !important;
  padding-right: 12px !important;
}

.custom-dialog .el-form {
  padding: 0 20px;
}

/* 确保所有表单项标签对齐 */
.custom-dialog .el-form-item {
  display: flex;
  align-items: center !important; /* 居中对齐 */
}

.custom-dialog .el-form-item__content {
  margin-left: 0 !important;
  flex: 1;
  display: flex;
  align-items: center !important; /* 居中对齐 */
}

/* 调整El-Radio-Group样式 */
.custom-dialog .el-radio-group {
  display: flex;
  padding-top: 0 !important; /* 移除上边距 */
  align-items: center !important; /* 居中对齐 */
}

/* 移除textarea的上边距 */
.custom-dialog .el-textarea {
  margin-top: 0 !important;
}

/* 调整必填项红色星号的位置 */
.custom-dialog .el-form-item.is-required .el-form-item__label:before {
  position: relative;
  top: 0;
}

/* 文本域表单项特殊处理 */
.custom-dialog .el-form-item.custom-form-item:has(.el-textarea) {
  align-items: flex-start !important;
}

.custom-dialog .el-form-item.custom-form-item:has(.el-textarea) .el-form-item__label {
  margin-top: 8px;
}

/* 适配不支持:has选择器的浏览器的备选方案 */
.custom-dialog .textarea-form-item {
  align-items: flex-start !important;
}

.custom-dialog .textarea-form-item .el-form-item__label {
  margin-top: 8px;
}
</style>

<style scoped>
.declaration-link:hover {
  color: #409EFF !important; /* Element UI的主蓝色 */
}

/* 搜索栏样式 - 仅应用于当前页面 */
.project-search-form {
  margin-bottom: 20px;
}

.project-search-form .search-form-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 10px;
  padding-bottom: 10px; /* 为第二行留出间距 */
}

.project-search-form .el-form-item {
  margin-right: 0;
  margin-bottom: 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 第一行：前三个搜索字段 */
.project-search-form .el-form-item:nth-child(-n+3) {
  flex: 1;
  min-width: 200px;
  max-width: calc(33.33% - 10px);
}

/* 第二行：后面的字段和按钮 */
.project-search-form .el-form-item:nth-child(n+4):not(:last-child) {
  flex: 0 0 auto;
  min-width: 200px;
}

.project-search-form .el-form-item__label {
  float: none;
  line-height: 32px;
  padding: 0 5px 0 0;
  white-space: nowrap;
  display: inline-block;
  font-size: 13px; /* 稍微减小字体大小 */
}

.project-search-form .el-form-item__content {
  margin-left: 0 !important;
  display: inline-block;
  line-height: 32px;
}

.project-search-form .el-input,
.project-search-form .el-select,
.project-search-form .el-cascader {
  width: 150px; /* 稍微减小宽度，使布局更紧凑 */
}

/* 确保按钮组在第二行靠右对齐 */
.project-search-form .el-form-item:last-child {
  margin-right: 0;
  margin-left: auto;
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

/* 调整按钮间距 */
.project-search-form .el-button + .el-button {
  margin-left: 5px;
}

/* 移除水平滚动条样式，因为现在使用换行布局 */

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .project-search-form .search-form-container {
    gap: 8px; /* 减少间距 */
  }

  /* 移动端所有字段都占满宽度，垂直排列 */
  .project-search-form .el-form-item:nth-child(-n+3) {
    flex: 1 1 100%;
    max-width: 100%;
    min-width: auto;
  }

  .project-search-form .el-form-item:nth-child(n+4):not(:last-child) {
    flex: 1 1 100%;
    min-width: auto;
  }

  .project-search-form .el-input,
  .project-search-form .el-select,
  .project-search-form .el-cascader {
    width: 100%; /* 移动端占满宽度 */
  }

  .project-search-form .el-form-item__label {
    font-size: 12px; /* 更小的字体 */
  }

  /* 按钮组在移动端也占满宽度 */
  .project-search-form .el-form-item:last-child {
    flex: 1 1 100%;
    margin-left: 0;
    justify-content: center;
  }
}

/* 弹窗表单样式 */
.project-dialog-form ::v-deep .el-form-item__label {
  width: 160px !important; /* 增加标签宽度，确保长标签能够完整显示 */
  white-space: normal !important; /* 允许标签换行 */
  line-height: 1.2 !important; /* 减小行高，使多行标签更紧凑 */
  padding-bottom: 5px;
}

.project-dialog-form ::v-deep .el-form-item {
  margin-right: 10px;
  margin-bottom: 15px; /* 增加底部边距，使表单项之间有更好的间隔 */
}

.project-dialog-form ::v-deep .el-input,
.project-dialog-form ::v-deep .el-select,
.project-dialog-form ::v-deep .el-cascader {
  width: 100%; /* 使输入框占满整个内容区域 */
}

/* 修复上传组件的样式 */
.project-dialog-form ::v-deep .el-form-item__content {
  margin-left: 160px !important; /* 与label-width保持一致 */
  width: calc(100% - 160px); /* 确保内容区域宽度正确 */
}

/* 增加单选按钮组的间距 */
.form-item-with-space {
  margin-bottom: 22px !important;
}

.form-item-with-space ::v-deep .el-radio {
  margin-right: 30px;
  line-height: 32px;
}

.form-item-with-space ::v-deep .el-radio__label {
  padding-left: 8px;
}

/* 调整弹窗中表单的样式 */
::v-deep .el-dialog__body {
  padding: 20px 30px;
}

/* 修复文件上传组件样式 */
::v-deep .el-upload {
  width: auto;
  display: inline-block;
}

/* 确保下载模版按钮与上传组件在同一行 */
::v-deep .el-button.el-button--text {
  margin-left: 10px;
  vertical-align: middle;
}

/* 表格容器自适应样式 */
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 84px); /* 减去头部和其他元素的高度 */
  overflow: hidden;
}

/* 表格自适应填充可用空间 */
.table-with-scroll {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 修复表格布局，确保表格填充可用空间 */
::v-deep .el-table {
  flex: 1;
  width: 100% !important;
  height: 100% !important;
}

::v-deep .el-table__body-wrapper {
  overflow-x: auto !important;
  overflow-y: auto !important;
  height: calc(100% - 40px) !important; /* 减去表头高度 */
}

/* 隐藏表格原生水平滚动条 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px !important; /* 显示适当大小的滚动条 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
  border-radius: 4px;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 4px;
}

/* 固定滚动条样式 */
.fixed-scroll-container {
  position: fixed;
  bottom: 50px; /* 增加底部距离，避开分页区域 */
  left: 0;
  right: 0;
  margin: 0 auto;
  width: calc(100% - 40px);
  max-width: 1200px;
  height: 15px;
  z-index: 2000;
  background-color: #f5f7fa;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  overflow-x: auto;
  display: none; /* 默认隐藏，使用原生滚动条 */
}

.fixed-scroll-container::-webkit-scrollbar {
  height: 10px;
}

.fixed-scroll-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 5px;
}

.fixed-scroll-content {
  height: 1px;
}

/* 审核详情样式 */
.audit-detail {
  padding: 10px;
}

.audit-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.audit-item .label {
  font-weight: bold;
  width: 100px;
  flex-shrink: 0;
  text-align: right;
  padding-right: 10px;
}

.audit-item .value {
  word-break: break-all;
  flex: 1;
}

/* 固定列样式修复：与主体高度一致并预留横向滚动条高度 */
::v-deep .el-table__fixed-right,
::v-deep .el-table__fixed-left {
  z-index: 10;
  bottom: var(--pm-scrollbar, 10px); /* 自适应滚动条厚度（默认10px） */
}

/* 固定列 body 高度与主体一致（表头默认40px） */
::v-deep .el-table__fixed-right .el-table__fixed-body-wrapper,
::v-deep .el-table__fixed-left .el-table__fixed-body-wrapper {
  overflow-y: auto !important;
}


/* 分页组件样式 */
.pagination-container {
  margin-top: 10px;
  padding: 10px 0;
}

/* 辅导老师相关样式 */
.tutors-container {
  display: flex;
  flex-direction: column;
}

.tutors-list {
  margin-top: 10px;
}

.tutor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;
}

.tutor-item:last-child {
  border-bottom: none;
}

.tutor-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.tutor-dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tutor-dialog-buttons {
  display: flex;
  justify-content: flex-end;
}

/* 辅导老师详情样式 */
.tutor-detail-item {
  margin-bottom: 15px;
  display: flex;
}

.tutor-detail-item .label {
  font-weight: bold;
  width: 100px;
  flex-shrink: 0;
}

.tutor-detail-item .value {
  flex: 1;
}

.tutor-clickable {
  cursor: pointer;
  color: #409EFF;
}

.tutor-clickable:hover {
  text-decoration: underline;
}

/* 响应式布局调整 */
@media screen and (max-width: 1366px) {
  ::v-deep .el-table .cell {
    white-space: nowrap;
  }
}

/* 适配不同屏幕尺寸 */
@media screen and (min-width: 1600px) {
  .app-container {
    max-width: 1800px;
    margin: 0 auto;
  }
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.upload-demo .el-upload {
  width: auto;
}

.upload-demo .el-upload-dragger {
  width: 100%;
  height: 140px; /* 减小高度 */
  padding: 15px; /* 减小内边距 */
}

.upload-preview {
  max-width: 100%;
  max-height: 120px; /* 减小预览图高度 */
  object-fit: contain;
}

.upload-placeholder {
  text-align: center;
  padding: 10px; /* 减小内边距 */
}

.upload-placeholder .el-icon-upload {
  font-size: 32px; /* 减小图标大小 */
  color: #909399;
  margin-bottom: 8px;
}

.upload-placeholder .el-upload__text {
  color: #606266;
  font-size: 13px; /* 减小字体大小 */
  margin-bottom: 6px;
}

.upload-placeholder .el-upload__text em {
  color: #409EFF;
  font-style: normal;
}

.upload-placeholder .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 4px; /* 减小上边距 */
}

/* 调整文件列表样式 */
.upload-demo ::v-deep .el-upload-list {
  margin-top: 8px;
}

.upload-demo ::v-deep .el-upload-list__item {
  margin-top: 4px;
  padding: 4px 8px;
}

/* 上传配置样式 */
.upload-container {
  width: 100%;
}

.upload-settings {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-settings .el-form-item {
  margin-bottom: 0;
}

.upload-settings .el-select {
  width: 100%;
}

/* 进度条容器样式 */
.el-progress {
  margin-top: 10px;
}

.el-progress--circle,
.el-progress--dashboard {
  margin: 10px auto;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .upload-settings .el-form-item {
    margin-bottom: 10px;
  }
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.upload-demo .el-upload {
  width: auto;
}

.upload-demo .el-upload__tip {
  margin-top: 10px;
  line-height: 1.2;
}

.upload-progress {
  margin-top: 15px;
}

/* 调整按钮间距 */
.upload-demo .el-button + .el-button {
  margin-left: 10px;
}

/* 分数详情样式 */
.score-detail-container {
  .score-item {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 16px;
    }

    .judge-info {
      p {
        margin: 5px 0;
        color: #606266;

        strong {
          color: #303133;
        }
      }
    }
  }

  .average-score {
    text-align: center;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    h4 {
      margin: 0;
      color: #409eff;
      font-size: 18px;
    }
  }
}

/* 评分对话框样式 */
.judge-score-tip {
  margin-bottom: 15px;
}

/* 评语输入容器样式 */
.comment-input-container {
  position: relative;
}

.comment-template-btn {
  text-align: right;
  margin-top: 5px;
}

/* 评语模板对话框样式 */
.template-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.template-dialog-content {
  min-height: 300px;
}

.no-template {
  text-align: center;
  padding: 50px 0;
}

.template-cards {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-title {
  font-weight: bold;
  color: #303133;
  font-size: 16px;
}

.template-card-content {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  min-height: 60px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.template-card:hover .template-card-content {
  background-color: #f0f9ff;
}

.template-card:hover .template-title {
  color: #409eff;
}

/* 导出进度对话框样式 */
.export-progress-container {
  padding: 20px 0;
}

.export-status-info {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-item .label {
  font-weight: bold;
  width: 80px;
  flex-shrink: 0;
}

.status-item .value {
  flex: 1;
}

.export-progress-bar {
  margin: 20px 0;
}

.export-message {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.export-message i {
  margin-right: 8px;
  font-size: 16px;
}

.export-actions {
  margin: 20px 0;
  text-align: center;
}

.export-tips {
  margin-top: 20px;
}

/* 响应式操作栏样式 */
.responsive-actions .cell {
  overflow: visible !important;
}

.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-width: 0;
  width: 100%;
}

.action-btn {
  flex-shrink: 0;
  margin-right: 4px;
  padding: 7px 8px !important;
  font-size: 12px;
  white-space: nowrap;
  min-width: 0;
}

.action-btn.hidden-action {
  display: none !important;
}

.primary-action {
  order: -1; /* 确保详情按钮始终在最前面 */
}

.more-actions-dropdown {
  flex-shrink: 0;
  margin-left: 4px;
}

.more-actions-dropdown .el-button {
  padding: 7px 8px !important;
  font-size: 12px;
}

/* 确保下拉菜单项正确显示 */
.el-dropdown-menu__item[disabled] {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 在不同缩放比例下的优化 */
@media screen and (min-resolution: 120dpi) {
  .action-btn {
    padding: 6px 7px !important;
    font-size: 11px;
  }

  .more-actions-dropdown .el-button {
    padding: 6px 7px !important;
    font-size: 11px;
  }
}

@media screen and (min-resolution: 144dpi) {
  .action-btn {
    padding: 5px 6px !important;
    font-size: 10px;
  }

  .more-actions-dropdown .el-button {
    padding: 5px 6px !important;
    font-size: 10px;
  }
}

/* 针对高缩放比例的特殊处理 */
@media screen and (min-resolution: 168dpi) {
  .responsive-actions {
    width: 180px !important;
  }

  .action-btn {
    padding: 4px 5px !important;
    font-size: 9px;
    margin-right: 2px;
  }

  .more-actions-dropdown .el-button {
    padding: 4px 5px !important;
    font-size: 9px;
  }
}
</style>
